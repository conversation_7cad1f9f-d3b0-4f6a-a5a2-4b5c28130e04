package com.fls.workorder;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.todo.api.model.TodoTaskQuery;
import com.fls.todo.api.model.TodoTaskVo;

/**
 * 工单服务
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface RemoteWorkOrderService {

    /**
     * 待领任务查询
     *
     * @param query 查询参数
     * @return 带领任务列表
     */
    Page<TodoTaskVo> queryUnclaimedTasks(TodoTaskQuery query);

    /**
     * 查询指定用户id的待领任务总数
     * @param userId 用户id
     * @return 待领任务总数
     */
    Long queryUnclaimedTaskCount(String userId);
}
