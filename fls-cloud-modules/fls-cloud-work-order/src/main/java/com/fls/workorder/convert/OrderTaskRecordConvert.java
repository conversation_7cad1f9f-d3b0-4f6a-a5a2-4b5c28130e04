package com.fls.workorder.convert;

import com.fls.workorder.entity.WorkorderRecordEntity;
import com.fls.workorder.entity.WorkorderTaskDefinitionEntity;
import com.fls.workorder.entity.WorkorderTaskRecordEntity;
import com.fls.workorder.pojo.dto.OrderCreateTaskDTO;
import com.fls.workorder.pojo.dto.TaskCompleteDTO;
import com.fls.workorder.pojo.dto.TaskFollowDTO;
import com.fls.workorder.pojo.vo.TaskRecordVo;
import com.fls.workorder.pojo.vo.TaskSimpleVo;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * WorkflowConvert
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderTaskRecordConvert {

    @Mappings({
            @Mapping(target = "formJson", source = "formJson"),
            @Mapping(target = "idProcinst", source = "idProcinst"),
            @Mapping(target = "idProctask", source = "idProctask"),
            @Mapping(target = "idsAssignee", source = "idsAssignee"),
            @Mapping(target = "idsCandidate", source = "idsCandidate"),
            @Mapping(target = "idsCoOperator", source = "idsCoOperator"),
            @Mapping(target = "idTaskDefinition", source = "idTaskDefinition"),
            @Mapping(target = "idTaskInstance", source = "idTaskInstance"),
            @Mapping(target = "idTaskResource", source = "idTaskResource"),
            @Mapping(target = "idWorkorderRecord", source = "idWorkorderRecord"),
            @Mapping(target = "idWorkorderTaskRecord", source = "idWorkorderTaskRecord"),
            @Mapping(target = "owner", source = "owner"),
            @Mapping(target = "sourceProjectName", source = "sourceProjectName"),
            @Mapping(target = "taskDescription", source = "taskDescription"),
            @Mapping(target = "taskInstanceName", source = "taskInstanceName"),
            @Mapping(target = "taskRemarks", source = "taskRemarks"),
            @Mapping(target = "taskStatus", source = "taskStatus"),
    })
    TaskRecordVo entityToVo(WorkorderTaskRecordEntity entity);

    List<TaskRecordVo> recordEntityToVoList(List<WorkorderTaskRecordEntity> entityList);

    @BeanMapping(ignoreByDefault = true)
    @Mappings({
            @Mapping(target = "idsAssignee", source = "dto.assignees"),
            @Mapping(target = "idsCandidate", source = "dto.candidates"),
            @Mapping(target = "idProcinst", source = "dto.idProcInst"),
            @Mapping(target = "idProctask", source = "dto.idProcTask"),
            @Mapping(target = "idTaskDefinition", source = "dto.idTaskDefinition"),
            @Mapping(target = "idWorkorderRecord", source = "dto.idWorkorderRecord"),
            //工单定义
            @Mapping(target = "idTaskResource", source = "taskDefinition.idTaskResource"),
            @Mapping(target = "taskLimit", source = "taskDefinition.taskLimit"),
            @Mapping(target = "taskDescription", source = "taskDefinition.taskDescription"),
            //工单基础属性
            @Mapping(target = "idOrg", source = "order.idOrg"),
            @Mapping(target = "idBizUnit", source = "order.idBizUnit"),
            @Mapping(target = "idDepartment", source = "order.idDepartment"),
            @Mapping(target = "assetCode", source = "order.assetCode"),
            @Mapping(target = "createBy", source = "order.createBy"),
            @Mapping(target = "updateBy", source = "order.updateBy"),
            @Mapping(target = "taskInstanceName", source = "dto.taskName"),
            @Mapping(target = "sourceProjectName", source = "order.sourceProjectName"),
            @Mapping(target = "startTime", expression = "java(java.time.LocalDateTime.now())"),
    })
    WorkorderTaskRecordEntity taskDTOToEntity(OrderCreateTaskDTO dto, WorkorderTaskDefinitionEntity taskDefinition, WorkorderRecordEntity order);

    @Mappings({
            @Mapping(target = "idWorkorderTaskRecord", source = "idTask"),
            @Mapping(target = "updateBy", source = "operator"),
            @Mapping(target = "sourceProjectName", source = "sourceProjectName"),
            @Mapping(target = "idTaskInstance", source = "idTaskInstance"),
            @Mapping(target = "taskInstanceName", source = "taskInstanceName"),
            @Mapping(target = "taskRemarks", source = "taskRemarks"),
    })
    void updateTaskRecordByFollowUp(TaskFollowDTO followDTO, @MappingTarget WorkorderTaskRecordEntity record);

    @Mappings({
            @Mapping(target = "idWorkorderTaskRecord", source = "idTask"),
            @Mapping(target = "updateBy", source = "operator"),
            @Mapping(target = "sourceProjectName", source = "sourceProjectName"),
            @Mapping(target = "idTaskInstance", source = "idTaskInstance"),
            @Mapping(target = "taskInstanceName", source = "taskInstanceName"),
    })
    void updateTaskRecordByComplete(TaskCompleteDTO completeDTO, @MappingTarget WorkorderTaskRecordEntity record);

    @Mappings({
            @Mapping(target = "idProcInst", source = "idProcinst"),
            @Mapping(target = "idProcTask", source = "idProctask"),
            @Mapping(target = "idsAssignee", source = "idsAssignee"),
            @Mapping(target = "idsCandidate", source = "idsCandidate"),
            @Mapping(target = "idsCoOperator", source = "idsCoOperator"),
            @Mapping(target = "idWorkorderRecord", source = "idWorkorderRecord"),
            @Mapping(target = "idWorkorderTaskRecord", source = "idWorkorderTaskRecord"),
            @Mapping(target = "taskStatus", source = "taskStatus"),
            @Mapping(target = "idTaskResource", source = "idTaskResource"),
    })
    TaskSimpleVo taskToSimpleVo(WorkorderTaskRecordEntity entity);
}
