package com.fls.workorder.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.todo.api.model.TodoTaskQuery;
import com.fls.todo.api.model.TodoTaskVo;
import com.fls.workorder.RemoteWorkOrderService;
import com.fls.workorder.service.IWorkorderTaskRecordService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * BaseAttrsServiceImpl
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteWorkorderServiceImpl implements RemoteWorkOrderService {

    private final IWorkorderTaskRecordService taskRecordService;

    @Override
    public Page<TodoTaskVo> queryUnclaimedTasks(TodoTaskQuery query) {
        // 调用taskRecordService查询待领任务
        return taskRecordService.queryUnclaimedTasks(query);
    }

    @Override
    public Long queryUnclaimedTaskCount(String userId) {
        return 0L;
    }
}
