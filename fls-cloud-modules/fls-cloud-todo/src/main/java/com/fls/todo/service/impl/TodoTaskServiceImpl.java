package com.fls.todo.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.master.api.RemoteResourceService;
import com.fls.master.api.model.ResourceInfo;
import com.fls.todo.api.enums.TaskHandleStatusEnum;
import com.fls.todo.api.enums.TodoTaskStatusEnum;
import com.fls.todo.api.model.TodoTaskQuery;
import com.fls.todo.api.model.TodoTaskVo;
import com.fls.todo.constant.TodoTaskConstant;
import com.fls.todo.convert.TodoTaskConvert;
import com.fls.todo.entity.TodoTaskEntity;
import com.fls.todo.enums.TaskTypeEnum;
import com.fls.todo.mapper.TodoTaskMapper;
import com.fls.todo.pojo.dto.TaskCancelDto;
import com.fls.todo.pojo.dto.TaskCompleteDto;
import com.fls.todo.pojo.dto.TaskHandleDto;
import com.fls.todo.pojo.dto.TaskResumeDto;
import com.fls.todo.pojo.dto.TaskSuspendDto;
import com.fls.todo.pojo.dto.TaskTransferDto;
import com.fls.todo.pojo.dto.TodoTaskDto;
import com.fls.todo.pojo.query.ApproveTaskQuery;
import com.fls.todo.pojo.query.StaticQuery;
import com.fls.todo.pojo.vo.ApproveTaskVo;
import com.fls.todo.pojo.vo.TaskMonthStaticVo;
import com.fls.todo.pojo.vo.TaskStaticVo;
import com.fls.todo.pojo.vo.TaskTotalStaticVo;
import com.fls.todo.pojo.vo.TodoTaskStaticVo;
import com.fls.todo.service.ITodoTaskService;
import com.fls.workflow.api.RemoteWorkflowService;
import com.fls.workorder.RemoteWorkOrderService;
import com.fls.workorder.enums.AssigneeTypeEnum;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 待办任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Service
@RequiredArgsConstructor
public class TodoTaskServiceImpl extends ServiceImpl<TodoTaskMapper, TodoTaskEntity> implements ITodoTaskService {

    private final TodoTaskConvert taskConvert;

    private final RedissonClient redissonClient;

    @DubboReference
    private RemoteWorkOrderService remoteWorkOrderService;

    @DubboReference
    private RemoteWorkflowService remoteWorkflowService;

    @DubboReference
    private RemoteResourceService resourceService;

    @Override
    public TodoTaskEntity getTodoTaskBySourceId(String idSource, String assignee) {
        if (StrUtil.isBlank(idSource)) {
            throw new IllegalArgumentException("idSource cannot be null or blank");
        }
        LambdaQueryWrapper<TodoTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TodoTaskEntity::getIdSourceRecord, idSource).eq(TodoTaskEntity::getIdAssignee, assignee)
            .orderByDesc(TodoTaskEntity::getCreateTime)
            .last("LIMIT 1");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public PageResult<?> queryTodoTasks(TodoTaskQuery query) {
        String queryStatus = query.getStatus();
        if (StrUtil.equals(TodoTaskStatusEnum.UNCLAIM.getCode(), queryStatus)) {
            Page<TodoTaskVo> todoTaskVoPage = remoteWorkOrderService.queryUnclaimedTasks(query);
            todoTaskVoPage.getRecords().forEach(this::extracted);
            return new PageResult<>(todoTaskVoPage, todoTaskVoPage.getRecords());
        }
        Page<TodoTaskVo> page = new Page<>(query.getPageNo(), query.getPageSize());
        Page<TodoTaskVo> todoPage = baseMapper.selectTodoTaskPage(page, query);
        todoPage.getRecords().forEach(this::extracted);
        return new PageResult<>(page, todoPage.getRecords());
    }

    @Override
    public PageResult<?> queryApproveTasks(ApproveTaskQuery query) {
        Page<ApproveTaskVo> page = new Page<>(query.getPageNo(), query.getPageSize());
        Page<ApproveTaskVo> todoPage = baseMapper.selectApproveTaskPage(page, query);
        todoPage.getRecords().forEach(approveTaskVo -> {
            approveTaskVo.setTaskStatusDesc(TodoTaskStatusEnum.getName(approveTaskVo.getTaskStatus()));
            approveTaskVo.setHandleStatusDesc(TaskHandleStatusEnum.getName(approveTaskVo.getHandleStatus()));
        });
        return new PageResult<>(page, todoPage.getRecords());
    }

    private void extracted(TodoTaskVo task) {
        task.setTaskTypeDesc(TaskTypeEnum.getName(task.getTaskType()));
        task.setAssigneeTypeDesc(AssigneeTypeEnum.getName(task.getAssigneeType()));
        task.setTaskStatusDesc(TodoTaskStatusEnum.getName(task.getTaskStatus()));
        task.setHandleStatusDesc(TaskHandleStatusEnum.getName(task.getHandleStatus()));
    }

    @Override
    public String addTodoTask(TodoTaskDto todoTaskDto) {
        checkResource(todoTaskDto);
        String idInstance = todoTaskDto.getIdInstance();
        String assignees = todoTaskDto.getAssignees();
        // 按逗号分隔待办人员
        String[] assigneeArray = assignees.split(",");
        List<String> createdTaskIds = new ArrayList<>();
        for (String assignee : assigneeArray) {
            assignee = assignee.trim();
            if (StrUtil.isBlank(assignee)) {
                continue;
            }
            // 幂等性校验
            Long count = lambdaQuery().eq(TodoTaskEntity::getIdSourceRecord, idInstance)
                .eq(TodoTaskEntity::getIdAssignee, assignee)
                .eq(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.PENDING_HANDLE.getCode())
                .count();
            if (count > 0) {
                // 如果该待办人已存在待办任务，跳过
                continue;
            }
            // 转换为待办任务实体
            TodoTaskEntity todoTaskEntity = taskConvert.dtoToTodoTaskEntity(todoTaskDto);
            // 设置当前待办人
            todoTaskEntity.setSourceRecordCode(todoTaskDto.getInstanceCode());
            todoTaskEntity.setTaskCode(todoTaskDto.getInstanceCode());
            todoTaskEntity.setIdBizunit(todoTaskDto.getIdBizunit());
            todoTaskEntity.setInitiator(todoTaskDto.getInitiator());
            todoTaskEntity.setIdAssignee(assignee);
            todoTaskEntity.setTaskStatus(TodoTaskStatusEnum.PENDING_HANDLE.getCode());
            todoTaskEntity.setCreateBy(TodoTaskConstant.SYSTEM_OPERATOR);
            todoTaskEntity.setCreateTime(LocalDateTime.now());
            todoTaskEntity.setUpdateBy(TodoTaskConstant.SYSTEM_OPERATOR);
            todoTaskEntity.setUpdateTime(LocalDateTime.now());
            todoTaskEntity.setTaskType(TodoTaskConstant.BIZ_TASK_TYPE);

            // 设置开始时间为当前时间
            todoTaskEntity.setStartTime(LocalDateTime.now());

            // 保存实体
            save(todoTaskEntity);
            createdTaskIds.add(todoTaskEntity.getIdTodoTask());
        }
        // 返回所有创建的任务ID，用逗号分隔
        return String.join(",", createdTaskIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handle(TaskHandleDto taskHandleDto) {
        String operator = taskHandleDto.getOperator();
        String idSourceBill = taskHandleDto.getIdBill();
        TodoTaskEntity taskEntity = lambdaQuery().eq(TodoTaskEntity::getIdSourceRecord, idSourceBill)
            .eq(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.PENDING_APPROVAL.getCode())
            .eq(TodoTaskEntity::getHandleStatus, TaskHandleStatusEnum.UN_READ.getCode())
            .eq(TodoTaskEntity::getTaskType, TaskTypeEnum.APPROVE.getCode())
            .eq(TodoTaskEntity::getIdAssignee, operator).one();
        if (ObjectUtil.isNull(taskEntity)) {
            throw new ServiceException("未阅待审任务不存在");
        }
        String lockKey = TodoTaskConstant.LOCK_TODO_TASK_COMPLETE_PREFIX + idSourceBill;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，等待5秒，锁定10秒后自动释放
            if (lock.tryLock(TodoTaskConstant.LOCK_WAIT_TIME, TodoTaskConstant.LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                try {
                    // 完成当前待办任务
                    taskEntity.setHandleStatus(TaskHandleStatusEnum.WAIT_HANDLE.getCode());
                    taskEntity.setUpdateBy(operator);
                    taskEntity.setUpdateTime(LocalDateTime.now());
                    updateById(taskEntity);

                    // 如果是业务待办类型，自动完成同单据的其他待办任务
                    if (StrUtil.equals(taskEntity.getTaskType(), TaskTypeEnum.APPROVE.getCode())) {
                        // 查找同一业务单据的其他待办任务
                        List<TodoTaskEntity> relatedTasks = lambdaQuery()
                            .eq(TodoTaskEntity::getIdSourceRecord, idSourceBill)
                            .eq(TodoTaskEntity::getTaskType, TaskTypeEnum.APPROVE.getCode())
                            // 排除当前任务
                            .ne(TodoTaskEntity::getIdTodoTask, taskEntity.getIdTodoTask())
                            // 排除已完成的任务
                            .ne(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.COMPLETED.getCode())
                            .list();

                        if (ObjectUtil.isNotEmpty(relatedTasks)) {
                            // 批量更新其他待办任务为已完成状态
                            for (TodoTaskEntity relatedTask : relatedTasks) {
                                relatedTask.setTaskStatus(TodoTaskStatusEnum.COMPLETED.getCode());
                                relatedTask.setUpdateBy(operator);
                                relatedTask.setUpdateTime(LocalDateTime.now());
                                relatedTask.setEndTime(LocalDateTime.now());
                            }
                            updateBatchById(relatedTasks);
                        }
                    }
                } finally {
                    lock.unlock();
                }
            } else {
                throw new ServiceException("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            throw new ServiceException("系统繁忙，请稍后重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transfer(TaskTransferDto taskTransferDto) {
        String idTodoTask = taskTransferDto.getIdTodoTask();
        String idSourceRecord = taskTransferDto.getIdSourceRecord();
        String operator = taskTransferDto.getOperator();
        String idTransfer = taskTransferDto.getIdTransfer();
        // 验证参数：至少需要提供一个查询条件
        if (StrUtil.isBlank(idTodoTask) && StrUtil.isBlank(idSourceRecord)) {
            throw new ServiceException("请提供待办任务ID或来源单据ID");
        }
        // 情况1：按待办任务ID转办
        if (StrUtil.isNotBlank(idTodoTask)) {
            transferSingleTask(idTodoTask, operator, idTransfer);
        }
        // 情况2：按来源单据ID转办
        else if (StrUtil.isNotBlank(idSourceRecord)) {
            transferTasksBySourceRecord(idSourceRecord, operator, idTransfer);
        }
    }

    /**
     * 转办单条待办任务
     *
     * @param idTodoTask 待办任务ID
     * @param operator   操作人
     * @param idTransfer 转办人用户ID
     */
    private void transferSingleTask(String idTodoTask, String operator, String idTransfer) {
        TodoTaskEntity taskEntity = lambdaQuery().eq(TodoTaskEntity::getIdTodoTask, idTodoTask)
            .eq(TodoTaskEntity::getIdAssignee, operator).one();
        if (ObjectUtil.isNull(taskEntity)) {
            throw new ServiceException("待办任务不存在");
        }
        // 检查任务是否已完成
        if (TodoTaskStatusEnum.isComplete(taskEntity.getTaskStatus())) {
            throw new ServiceException("任务已完成或已关闭，无法进行转办操作");
        }
        // 使用业务单据ID作为分布式锁key
        String lockKey = TodoTaskConstant.LOCK_TODO_TASK_TRANSFER_PREFIX + taskEntity.getIdSourceRecord();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            if (lock.tryLock(TodoTaskConstant.LOCK_WAIT_TIME, TodoTaskConstant.LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                try {
                    // 更新任务的办理人为转办人
                    taskEntity.setIdAssignee(idTransfer);
                    taskEntity.setUpdateBy(operator);
                    taskEntity.setUpdateTime(LocalDateTime.now());
                    taskEntity.setHandleStatus(TaskHandleStatusEnum.WAIT_HANDLE.getCode());
                    updateById(taskEntity);
                } finally {
                    lock.unlock();
                }
            } else {
                throw new ServiceException("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            throw new ServiceException("系统繁忙，请稍后重试");
        }
    }

    /**
     * 按来源单据ID转办待办任务
     *
     * @param idSourceRecord 来源单据ID
     * @param operator       操作人
     * @param idTransfer     转办人用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void transferTasksBySourceRecord(String idSourceRecord, String operator, String idTransfer) {
        // 使用业务单据ID作为分布式锁key
        String lockKey = TodoTaskConstant.LOCK_TODO_TASK_TRANSFER_PREFIX + idSourceRecord;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(TodoTaskConstant.LOCK_WAIT_TIME, TodoTaskConstant.LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                try {
                    // 按照来源单据ID和办理人查询到原始的待办任务信息（必须是未完成状态）
                    TodoTaskEntity originalTask = lambdaQuery()
                        .eq(TodoTaskEntity::getIdSourceRecord, idSourceRecord)
                        .eq(TodoTaskEntity::getIdAssignee, operator)
                        .in(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.PENDING_APPROVAL.getCode(), TodoTaskStatusEnum.PENDING_HANDLE.getCode())
                        .one();

                    if (ObjectUtil.isNull(originalTask)) {
                        throw new ServiceException("未找到可转办的待办任务或任务已完成");
                    }

                    // 更新原始任务的办理人为转办人
                    originalTask.setIdAssignee(idTransfer);
                    originalTask.setUpdateBy(operator);
                    originalTask.setUpdateTime(LocalDateTime.now());
                    originalTask.setHandleStatus(TaskHandleStatusEnum.WAIT_HANDLE.getCode());
                    updateById(originalTask);

                    // 查询来源单据的其他待办任务（非工单任务类型）
                    List<TodoTaskEntity> otherTasks = lambdaQuery()
                        .eq(TodoTaskEntity::getIdSourceRecord, idSourceRecord)
                        // 排除工单任务类型
                        .ne(TodoTaskEntity::getTaskType, TaskTypeEnum.ORDER.getCode())
                        // 排除当前转办的任务
                        .ne(TodoTaskEntity::getIdTodoTask, originalTask.getIdTodoTask())
                        .in(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.PENDING_APPROVAL.getCode(), TodoTaskStatusEnum.PENDING_HANDLE.getCode())
                        .list();

                    // 将其他待办任务自动完成
                    if (ObjectUtil.isNotEmpty(otherTasks)) {
                        for (TodoTaskEntity otherTask : otherTasks) {
                            // 设置为已办理状态
                            otherTask.setUpdateBy(operator);
                            otherTask.setUpdateTime(LocalDateTime.now());
                            otherTask.setEndTime(LocalDateTime.now());
                            otherTask.setTaskStatus(TodoTaskStatusEnum.COMPLETED.getCode());
                        }
                        updateBatchById(otherTasks);
                    }
                } finally {
                    lock.unlock();
                }
            } else {
                throw new ServiceException("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            throw new ServiceException("系统繁忙，请稍后重试");
        }
    }

    private void checkResource(TodoTaskDto todoTaskDto) {
        String idResource = todoTaskDto.getIdResource();
        ResourceInfo resourceById = resourceService.getResourceById(idResource);
        if (ObjectUtil.isNull(resourceById)) {
            throw new ServiceException("资源不存在");
        }
        if (StrUtil.isBlank(todoTaskDto.getVisitPath()) && StrUtil.isBlank(todoTaskDto.getVisitPath())) {
            todoTaskDto.setVisitPath(resourceById.getIncalLink());
            todoTaskDto.setAppletVisitPath(resourceById.getAppLink());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeTodoTask(TaskCompleteDto finishDto) {
        String idTodoTask = finishDto.getIdTodoTask();
        String operator = finishDto.getOperator();
        String idSourceBill = finishDto.getIdSourceBill();
        if (StrUtil.isNotBlank(idSourceBill)) {
            TodoTaskEntity taskEntity = lambdaQuery().eq(TodoTaskEntity::getIdSourceRecord, idSourceBill)
                .in(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.PENDING_HANDLE.getCode(), TodoTaskStatusEnum.PENDING_APPROVAL.getCode())
                .eq(TodoTaskEntity::getIdAssignee, operator).one();
            if (ObjectUtil.isNull(taskEntity)) {
                throw new ServiceException("待办任务不存在");
            }
            idTodoTask = taskEntity.getIdTodoTask();
        }

        // 先获取待办任务信息，用于构建锁key
        TodoTaskEntity todoTask = getById(idTodoTask);
        if (ObjectUtil.isNull(todoTask)) {
            throw new ServiceException("待办任务不存在");
        }
        if (!StrUtil.equals(todoTask.getIdAssignee(), operator)) {
            throw new ServiceException("待办任务无操作权限");
        }
        if (TodoTaskStatusEnum.isComplete(todoTask.getTaskStatus())) {
            throw new ServiceException("待办任务已完成");
        }

        // 使用业务单据ID作为分布式锁key，防止多人并发处理同一单据
        String lockKey = TodoTaskConstant.LOCK_TODO_TASK_COMPLETE_PREFIX + todoTask.getIdSourceRecord();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁，等待5秒，锁定10秒后自动释放
            if (lock.tryLock(TodoTaskConstant.LOCK_WAIT_TIME, TodoTaskConstant.LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                try {
                    // 完成当前待办任务
                    todoTask.setTaskStatus(TodoTaskStatusEnum.COMPLETED.getCode());
                    todoTask.setHandleStatus(TaskHandleStatusEnum.COMPLETED.getCode());
                    todoTask.setUpdateBy(operator);
                    todoTask.setUpdateTime(LocalDateTime.now());
                    updateById(todoTask);

                    // 查找同一业务单据的其他待办任务
                    List<TodoTaskEntity> relatedTasks = lambdaQuery()
                        .eq(TodoTaskEntity::getIdSourceRecord, todoTask.getIdSourceRecord())
                        // 排除当前任务
                        .ne(TodoTaskEntity::getIdTodoTask, idTodoTask)
                        // 排除已完成的任务
                        .ne(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.COMPLETED.getCode())
                        .list();

                    if (ObjectUtil.isNotEmpty(relatedTasks)) {
                        // 批量更新其他待办任务为已完成状态
                        for (TodoTaskEntity relatedTask : relatedTasks) {
                            relatedTask.setTaskStatus(TodoTaskStatusEnum.COMPLETED.getCode());
                            relatedTask.setUpdateBy(operator);
                            relatedTask.setUpdateTime(LocalDateTime.now());
                            relatedTask.setEndTime(LocalDateTime.now());
                        }
                        updateBatchById(relatedTasks);
                    }
                } finally {
                    lock.unlock();
                }
            } else {
                throw new ServiceException("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            throw new ServiceException("系统繁忙，请稍后重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTodoTask(TaskCancelDto cancelDto) {
        String idTodoTask = cancelDto.getIdTodoTask();
        String idSourceRecord = cancelDto.getIdSourceRecord();
        String operator = cancelDto.getOperator();
        // 验证参数：至少需要提供一个取消条件
        if (StrUtil.isBlank(idTodoTask) && StrUtil.isBlank(idSourceRecord)) {
            throw new ServiceException("请提供待办任务ID或业务单据ID");
        }
        // 按单条任务取消
        if (StrUtil.isNotBlank(idTodoTask)) {
            cancelSingleTask(idTodoTask, operator);
        }
        // 按业务单据取消
        if (StrUtil.isNotBlank(idSourceRecord)) {
            cancelTasksBySourceRecord(idSourceRecord, operator);
        }
    }

    /**
     * 取消单条待办任务
     *
     * @param idTodoTask 待办任务ID
     * @param operator   操作人
     */
    private void cancelSingleTask(String idTodoTask, String operator) {
        TodoTaskEntity todoTask = getById(idTodoTask);
        if (ObjectUtil.isNull(todoTask)) {
            throw new ServiceException("待办任务不存在");
        }
        // 使用业务单据ID作为分布式锁key
        String lockKey = TodoTaskConstant.LOCK_TODO_TASK_CANCEL_PREFIX + todoTask.getIdSourceRecord();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(TodoTaskConstant.LOCK_WAIT_TIME, TodoTaskConstant.LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                try {
                    // 检查任务是否已完成或已终止
                    if (TodoTaskStatusEnum.isComplete(todoTask.getTaskStatus())) {
                        throw new ServiceException("任务已完成或已终止，无法取消");
                    }
                    // 设置任务状态为已关闭（已终止）
                    todoTask.setTaskStatus(TodoTaskStatusEnum.TERMINATED.getCode());
                    todoTask.setUpdateBy(operator);
                    todoTask.setUpdateTime(LocalDateTime.now());
                    todoTask.setEndTime(LocalDateTime.now());
                    updateById(todoTask);
                } finally {
                    lock.unlock();
                }
            } else {
                throw new ServiceException("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            throw new ServiceException("系统繁忙，请稍后重试");
        }
    }

    /**
     * 按业务单据取消待办任务
     *
     * @param idSourceRecord 业务单据ID
     * @param operator       操作人
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelTasksBySourceRecord(String idSourceRecord, String operator) {
        // 使用业务单据ID作为分布式锁key
        String lockKey = TodoTaskConstant.LOCK_TODO_TASK_CANCEL_PREFIX + idSourceRecord;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(TodoTaskConstant.LOCK_WAIT_TIME, TodoTaskConstant.LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                try {
                    // 查找该业务单据的所有业务待办任务
                    List<TodoTaskEntity> todoTasks = lambdaQuery()
                        .eq(TodoTaskEntity::getIdSourceRecord, idSourceRecord)
                        .ne(TodoTaskEntity::getTaskType, TaskTypeEnum.ORDER.getCode())
                        .in(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.PENDING_HANDLE.getCode(), TodoTaskStatusEnum.PENDING_APPROVAL.getCode())
                        .list();

                    if (todoTasks.isEmpty()) {
                        throw new ServiceException("未找到可取消的业务待办任务");
                    }
                    // 批量取消任务
                    for (TodoTaskEntity todoTask : todoTasks) {
                        todoTask.setTaskStatus(TodoTaskStatusEnum.TERMINATED.getCode());
                        todoTask.setUpdateBy(operator);
                        todoTask.setUpdateTime(LocalDateTime.now());
                        todoTask.setEndTime(LocalDateTime.now());
                    }
                    updateBatchById(todoTasks);
                } finally {
                    lock.unlock();
                }
            } else {
                throw new ServiceException("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            throw new ServiceException("系统繁忙，请稍后重试");
        }
    }

    @Override
    public void suspend(TaskSuspendDto suspendDto) {
        String idTodoTask = suspendDto.getIdTodoTask();
        String userId = suspendDto.getUserId();
        // 查询任务
        TodoTaskEntity todoTask = getById(idTodoTask);
        if (ObjectUtil.isNull(todoTask)) {
            throw new ServiceException("待办任务不存在");
        }
        // 校验办理人
        if (!StrUtil.equals(todoTask.getIdAssignee(), userId)) {
            throw new ServiceException("无权操作该待办任务");
        }
        // 更新状态为已挂起
        todoTask.setTaskStatus(TodoTaskStatusEnum.SUSPEND.getCode());
        todoTask.setUpdateBy(userId);
        todoTask.setUpdateTime(LocalDateTime.now());
        updateById(todoTask);
    }

    @Override
    public List<TodoTaskEntity> getCoOptTasksBySourceId(String idSource) {
        if (StrUtil.isBlank(idSource)) {
            throw new IllegalArgumentException("idSource cannot be null or blank");
        }
        LambdaQueryWrapper<TodoTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TodoTaskEntity::getIdSourceRecord, idSource)
            .eq(TodoTaskEntity::getTaskType, TaskTypeEnum.ORDER.getCode())
            .eq(TodoTaskEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            .eq(TodoTaskEntity::getAssigneeType, AssigneeTypeEnum.COOPERATE.getType())
            .eq(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.PENDING_HANDLE.getCode());
        List<TodoTaskEntity> todoTaskEntities = getBaseMapper().selectList(queryWrapper);
        return todoTaskEntities == null ? Collections.emptyList() : todoTaskEntities;
    }

    @Override
    public TodoTaskStaticVo getTaskStaticOverview(StaticQuery query) {
        // 获取待办任务统计
        TaskStaticVo assigneeTask = getAssigneeTaskStatic(query);
        // 获取待审任务统计
        query.setAssignee(false);
        TaskStaticVo approveStaticVo = getAssigneeTaskStatic(query);
        TodoTaskStaticVo result = new TodoTaskStaticVo();
        result.setAssigneeTask(assigneeTask);
        result.setApproveTask(approveStaticVo);
        return result;
    }

    @Override
    public TaskTotalStaticVo getTaskTotal(String userId) {
        TaskTotalStaticVo result = new TaskTotalStaticVo();
        // 1. 统计待领任务总数 - 通过dubbo调用工单服务
        Long claimCount = remoteWorkOrderService.queryUnclaimedTaskCount(userId);
        result.setClaimCount(claimCount != null ? claimCount : 0);
        // 2. 统计待办任务总数 - 业务待办(task_type=1) + 工单待办(task_type=0)，包括待办理(task_status=1)和已挂起(task_status=6)
        // 业务待办 - 待办理状态
        Long bizHandleCount = baseMapper.countByTaskTypeAndStatus(TaskTypeEnum.BIZ.getCode(), TodoTaskStatusEnum.PENDING_HANDLE.getCode(), userId);
        // 业务待办 - 已挂起状态
        Long bizSuspendCount = baseMapper.countByTaskTypeAndStatus(TaskTypeEnum.BIZ.getCode(), TodoTaskStatusEnum.SUSPEND.getCode(), userId);
        // 工单待办 - 待办理状态
        Long orderHandleCount = baseMapper.countByTaskTypeAndStatus(TaskTypeEnum.ORDER.getCode(), TodoTaskStatusEnum.PENDING_HANDLE.getCode(), userId);
        // 工单待办 - 已挂起状态
        Long orderSuspendCount = baseMapper.countByTaskTypeAndStatus(TaskTypeEnum.ORDER.getCode(), TodoTaskStatusEnum.SUSPEND.getCode(), userId);

        Long totalHandleCount = (bizHandleCount != null ? bizHandleCount : 0) +
                               (bizSuspendCount != null ? bizSuspendCount : 0) +
                               (orderHandleCount != null ? orderHandleCount : 0) +
                               (orderSuspendCount != null ? orderSuspendCount : 0);
        result.setHandleCount(totalHandleCount);
        // 3. 统计待审任务总数 - task_type为2，task_status为0，id_assignee为指定用户id
        Long approveCount = baseMapper.countByTaskTypeAndStatus(TaskTypeEnum.APPROVE.getCode(), TodoTaskStatusEnum.PENDING_APPROVAL.getCode(), userId);
        result.setApproveCount(approveCount != null ? approveCount : 0);
        return result;
    }

    private TaskStaticVo getAssigneeTaskStatic(StaticQuery query) {
        LocalDateTime startTime = getStartTimeByType(query.getType());
        LocalDateTime endTime = LocalDateTime.now();

        LambdaQueryWrapper<TodoTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(TodoTaskEntity::getCreateTime, startTime)
            .le(TodoTaskEntity::getCreateTime, endTime)
            .eq(StrUtil.isNotBlank(query.getUserId()), TodoTaskEntity::getIdAssignee, query.getUserId())
            .eq(TodoTaskEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);

        if (query.isAssignee()) {
            queryWrapper.in(TodoTaskEntity::getTaskType, Arrays.asList(TaskTypeEnum.ORDER.getCode(), TaskTypeEnum.BIZ.getCode()));
        } else {
            queryWrapper.eq(TodoTaskEntity::getTaskType, TaskTypeEnum.APPROVE.getCode());
        }
        // 总数
        int total = Math.toIntExact(baseMapper.selectCount(queryWrapper));

        // 待处理数量（待办理状态）
        if (query.isAssignee()) {
            queryWrapper.eq(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.PENDING_HANDLE.getCode());
        } else {
            queryWrapper.eq(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.PENDING_APPROVAL.getCode());
        }
        int handle = Math.toIntExact(baseMapper.selectCount(queryWrapper));

        TaskStaticVo taskStatic = new TaskStaticVo();
        taskStatic.setTotal(total);
        taskStatic.setHandle(handle);

        return taskStatic;
    }

    private LocalDateTime getStartTimeByType(String type) {
        LocalDateTime now = LocalDateTime.now();
        switch (type) {
            case "day":
                return now.toLocalDate().atStartOfDay();
            case "week":
                return now.with(DayOfWeek.MONDAY).toLocalDate().atStartOfDay();
            case "month":
                return now.withDayOfMonth(1).toLocalDate().atStartOfDay();
            default:
                throw new IllegalArgumentException("不支持的统计类型: " + type);
        }
    }

    @Override
    public List<TaskMonthStaticVo> getTaskMonthStatic(Integer year, String userId) {
        // 获取待办任务月度统计
        Map<String, Integer> assigneeMonthData = getAssigneeTaskMonthStatic(year, userId, true);

        // 获取待审任务月度统计
        Map<String, Integer> approveMonthData = getAssigneeTaskMonthStatic(year, userId, false);

        // 构建12个月的完整数据
        List<TaskMonthStaticVo> monthList = new ArrayList<>();
        for (int month = 1; month <= 12; month++) {
            String monthKey = String.format("%d-%02d", year, month);

            TaskMonthStaticVo item = new TaskMonthStaticVo();
            item.setMonth(monthKey);
            item.setAssigneeCount(assigneeMonthData.getOrDefault(monthKey, 0));
            item.setApproveCount(approveMonthData.getOrDefault(monthKey, 0));

            monthList.add(item);
        }

        return monthList;
    }

    private Map<String, Integer> getAssigneeTaskMonthStatic(Integer year, String userId, boolean isAssignee) {
        LocalDateTime startTime = LocalDateTime.of(year, 1, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(year, 12, 31, 23, 59, 59);

        LambdaQueryWrapper<TodoTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(TodoTaskEntity::getCreateTime, startTime)
            .eq(StrUtil.isNotBlank(userId), TodoTaskEntity::getIdAssignee, userId)
            .le(TodoTaskEntity::getCreateTime, endTime)
            .eq(TodoTaskEntity::getDeleteFlag, "0");

        if (isAssignee) {
            queryWrapper.in(TodoTaskEntity::getTaskType, Arrays.asList(TaskTypeEnum.ORDER.getCode(), TaskTypeEnum.BIZ.getCode()));
        } else {
            queryWrapper.eq(TodoTaskEntity::getTaskType, TaskTypeEnum.APPROVE.getCode());
        }

        List<TodoTaskEntity> tasks = baseMapper.selectList(queryWrapper);

        Map<String, Integer> monthData = new HashMap<>();
        for (TodoTaskEntity task : tasks) {
            String monthKey = task.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            monthData.merge(monthKey, 1, Integer::sum);
        }

        return monthData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resumeTask(TaskResumeDto resumeDto) {
        String idSourceBill = resumeDto.getIdSourceBill();
        String operator = resumeDto.getOperator();
        // 1. 查询最近的未完成的一个任务
        TodoTaskEntity latestUnfinishedTask = lambdaQuery()
            .eq(TodoTaskEntity::getIdSourceRecord, idSourceBill)
            .eq(TodoTaskEntity::getIdAssignee, operator)
            .eq(TodoTaskEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED)
            // 查询未完成状态的任务（排除已办理3、已关闭4等状态）
            .eq(TodoTaskEntity::getTaskStatus, TodoTaskStatusEnum.COMPLETED.getCode())
            // 按创建时间倒序排列，取最新的一条
            .orderByDesc(TodoTaskEntity::getUpdateTime)
            .last("LIMIT 1")
            .one();
        // 2. 如果没有则直接跳过
        if (ObjectUtil.isNull(latestUnfinishedTask)) {
            return;
        }
        // 3. 按照任务类型进行状态更新
        String taskType = latestUnfinishedTask.getTaskType();
        if (StrUtil.equals(TaskTypeEnum.ORDER.getCode(), taskType)) {
            // 工单待办：更新为待处理状态
            latestUnfinishedTask.setTaskStatus(TodoTaskStatusEnum.PENDING_HANDLE.getCode());
        } else if (StrUtil.equals(TaskTypeEnum.APPROVE.getCode(), taskType)) {
            // 审批待办：更新为待审批状态
            latestUnfinishedTask.setTaskStatus(TodoTaskStatusEnum.PENDING_APPROVAL.getCode());
        }
        // 设置更新信息
        latestUnfinishedTask.setUpdateBy(operator);
        latestUnfinishedTask.setUpdateTime(LocalDateTime.now());
        // 更新任务状态
        updateById(latestUnfinishedTask);
    }
}
